import { NextRequest, NextResponse } from 'next/server'
import { getBenefits, createBenefit } from '@/lib/database'
import { requireAuth } from '@/lib/auth'
import type { BenefitCategory } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category') as BenefitCategory | null
    
    const benefits = await getBenefits(category || undefined)
    
    return NextResponse.json(benefits)
  } catch (error) {
    console.error('Error fetching benefits:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefits' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAuth()
    
    const body = await request.json()
    const { name, category, icon, description } = body

    if (!name || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const benefit = await createBenefit({
      name,
      category,
      icon,
      description: description || '',
    })

    return NextResponse.json(benefit, { status: 201 })
  } catch (error) {
    console.error('Error creating benefit:', error)
    return NextResponse.json(
      { error: 'Failed to create benefit' },
      { status: 500 }
    )
  }
}
