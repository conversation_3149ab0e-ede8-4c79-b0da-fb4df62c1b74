import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const sort = searchParams.get('sort') || 'created_at' // 'name', 'created_at'
    
    const offset = (page - 1) * limit
    
    let whereClause = ''
    const params: any[] = []
    let paramIndex = 1
    
    const conditions: string[] = []
    
    if (category && category !== 'all') {
      conditions.push(`b.category = $${paramIndex}`)
      params.push(category)
      paramIndex++
    }
    
    if (search) {
      conditions.push(`b.name ILIKE $${paramIndex}`)
      params.push(`%${search}%`)
      paramIndex++
    }
    
    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`
    }
    
    // Get benefits with usage statistics
    const benefitsQuery = `
      SELECT 
        b.*,
        COUNT(cb.id) as company_count,
        COUNT(CASE WHEN cb.is_verified = true THEN 1 END) as verified_count,
        COUNT(CASE WHEN cb.is_verified = false THEN 1 END) as unverified_count
      FROM benefits b
      LEFT JOIN company_benefits cb ON b.id = cb.benefit_id
      ${whereClause}
      GROUP BY b.id
      ORDER BY ${sort === 'name' ? 'b.name ASC' : 'b.created_at DESC'}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    params.push(limit, offset)
    
    const benefitsResult = await query(benefitsQuery, params)
    
    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT b.id) as total
      FROM benefits b
      ${whereClause}
    `
    
    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)
    
    return NextResponse.json({
      benefits: benefitsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Error fetching benefits for admin:', error)
    return NextResponse.json(
      { error: 'Failed to fetch benefits' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { name, category, icon } = body
    
    if (!name || !category) {
      return NextResponse.json(
        { error: 'Name and category are required' },
        { status: 400 }
      )
    }
    
    const result = await query(
      'INSERT INTO benefits (name, category, icon) VALUES ($1, $2, $3) RETURNING *',
      [name, category, icon]
    )
    
    return NextResponse.json(result.rows[0], { status: 201 })
    
  } catch (error) {
    console.error('Error creating benefit:', error)
    return NextResponse.json(
      { error: 'Failed to create benefit' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { benefitId, data } = body
    
    if (!benefitId || !data) {
      return NextResponse.json(
        { error: 'Benefit ID and data are required' },
        { status: 400 }
      )
    }
    
    const keys = Object.keys(data)
    const values = Object.values(data)
    const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')
    
    const result = await query(
      `UPDATE benefits SET ${setClause} WHERE id = $${keys.length + 1} RETURNING *`,
      [...values, benefitId]
    )

    const benefit = result.rows[0]
    return NextResponse.json({
      id: benefit.id,
      name: benefit.name,
      category: benefit.category,
      icon: benefit.icon,
      created_at: benefit.created_at?.toISOString()
    })
    
  } catch (error) {
    console.error('Error updating benefit:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const benefitId = searchParams.get('benefitId')
    
    if (!benefitId) {
      return NextResponse.json(
        { error: 'Benefit ID is required' },
        { status: 400 }
      )
    }
    
    await query('DELETE FROM benefits WHERE id = $1', [benefitId])
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('Error deleting benefit:', error)
    return NextResponse.json(
      { error: 'Failed to delete benefit' },
      { status: 500 }
    )
  }
}
