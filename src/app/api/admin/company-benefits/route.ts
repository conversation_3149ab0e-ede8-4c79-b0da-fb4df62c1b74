import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

// GET /api/admin/company-benefits - Get overview of all company benefits with verification status
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const companyId = searchParams.get('companyId')
    const benefitId = searchParams.get('benefitId')
    const verified = searchParams.get('verified') // 'true', 'false', or null for all
    const hasDisputes = searchParams.get('hasDisputes') // 'true', 'false', or null
    const search = searchParams.get('search') // search by company name
    
    const offset = (page - 1) * limit
    
    let whereConditions: string[] = []
    let params: any[] = []
    let paramIndex = 1
    
    if (companyId) {
      whereConditions.push(`cb.company_id = $${paramIndex}`)
      params.push(companyId)
      paramIndex++
    }
    
    if (benefitId) {
      whereConditions.push(`cb.benefit_id = $${paramIndex}`)
      params.push(benefitId)
      paramIndex++
    }
    
    if (verified !== null) {
      whereConditions.push(`cb.is_verified = $${paramIndex}`)
      params.push(verified === 'true')
      paramIndex++
    }

    if (hasDisputes !== null) {
      if (hasDisputes === 'true') {
        whereConditions.push(`(SELECT COUNT(*) FROM benefit_verifications bv WHERE bv.company_benefit_id = cb.id AND bv.status = 'disputed') > 0`)
      } else {
        whereConditions.push(`(SELECT COUNT(*) FROM benefit_verifications bv WHERE bv.company_benefit_id = cb.id AND bv.status = 'disputed') = 0`)
      }
    }

    if (search) {
      whereConditions.push(`c.name ILIKE $${paramIndex}`)
      params.push(`%${search}%`)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''
    
    // Get company benefits with verification counts and dispute information
    let sql = `
      SELECT 
        cb.id as company_benefit_id,
        cb.company_id,
        cb.benefit_id,
        cb.is_verified,
        cb.created_at,
        cb.created_at,
        c.name as company_name,
        c.domain as company_domain,
        c.verified as company_verified,
        b.name as benefit_name,
        b.category as benefit_category,
        b.icon as benefit_icon,
        -- Verification counts
        (SELECT COUNT(*) FROM benefit_verifications bv 
         WHERE bv.company_benefit_id = cb.id AND bv.status = 'confirmed') as confirmed_count,
        (SELECT COUNT(*) FROM benefit_verifications bv 
         WHERE bv.company_benefit_id = cb.id AND bv.status = 'disputed') as disputed_count,
        (SELECT COUNT(*) FROM benefit_verifications bv 
         WHERE bv.company_benefit_id = cb.id) as total_verifications,
        -- Check if has disputes
        (SELECT COUNT(*) FROM benefit_verifications bv 
         WHERE bv.company_benefit_id = cb.id AND bv.status = 'disputed') > 0 as has_disputes
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      ${whereClause}
      ORDER BY c.name, b.category, b.name
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    params.push(limit, offset)

    const result = await query(sql, params)
    
    // Get total count for pagination
    let countSql = `
      SELECT COUNT(*) as total
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      ${whereClause}
    `

    const countResult = await query(countSql, params.slice(0, -2)) // Remove limit and offset
    const total = parseInt(countResult.rows[0].total)
    
    return NextResponse.json({
      companyBenefits: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Error fetching company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to fetch company benefits' },
      { status: 500 }
    )
  }
}

// POST /api/admin/company-benefits - Bulk operations on company benefits
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { action, companyBenefitIds, reason } = body
    
    if (!action || !['verify', 'unverify', 'delete'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be "verify", "unverify", or "delete"' },
        { status: 400 }
      )
    }
    
    if (!companyBenefitIds || !Array.isArray(companyBenefitIds) || companyBenefitIds.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit IDs array is required' },
        { status: 400 }
      )
    }
    
    // Get company benefit details for response
    const placeholders = companyBenefitIds.map((_, i) => `$${i + 1}`).join(',')
    const detailsResult = await query(
      `SELECT cb.id, c.name as company_name, b.name as benefit_name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE cb.id IN (${placeholders})`,
      companyBenefitIds
    )
    
    if (detailsResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'No matching company benefits found' },
        { status: 404 }
      )
    }
    
    const affectedBenefits = detailsResult.rows
    
    if (action === 'delete') {
      // Delete company benefits (this will cascade delete verifications)
      await query(
        `DELETE FROM company_benefits WHERE id IN (${placeholders})`,
        companyBenefitIds
      )
      
      return NextResponse.json({
        success: true,
        message: `Deleted ${affectedBenefits.length} company benefit(s)`,
        details: {
          action,
          deletedCount: affectedBenefits.length,
          deletedBenefits: affectedBenefits.map(b => `${b.benefit_name} from ${b.company_name}`),
          ...(reason && { reason })
        }
      })
    } else {
      // Update verification status
      const isVerified = action === 'verify'
      const updatePlaceholders = companyBenefitIds.map((_, i) => `$${i + 2}`).join(',')
      await query(
        `UPDATE company_benefits
         SET is_verified = $1
         WHERE id IN (${updatePlaceholders})`,
        [isVerified, ...companyBenefitIds]
      )
      
      return NextResponse.json({
        success: true,
        message: `${action === 'verify' ? 'Verified' : 'Unverified'} ${affectedBenefits.length} company benefit(s)`,
        details: {
          action,
          updatedCount: affectedBenefits.length,
          updatedBenefits: affectedBenefits.map(b => `${b.benefit_name} from ${b.company_name}`),
          newStatus: isVerified,
          ...(reason && { reason })
        }
      })
    }
    
  } catch (error) {
    console.error('Error performing bulk operation on company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    )
  }
}
