'use client'

import { useState, useEffect, useMemo } from 'react'
import { Search, Check, X, Plus, ArrowRight, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Benefit {
  id: string
  name: string
  category: string
  icon?: string
}

interface BatchBenefitSelectionProps {
  companyId: string
  companyName: string
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  existingBenefitIds: string[]
}

type SelectionStep = 'select' | 'review' | 'submitting'

export function BatchBenefitSelection({
  companyId,
  companyName,
  isOpen,
  onClose,
  onSuccess,
  existingBenefitIds
}: BatchBenefitSelectionProps) {
  const [allBenefits, setAllBenefits] = useState<Benefit[]>([])
  const [selectedBenefitIds, setSelectedBenefitIds] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [currentStep, setCurrentStep] = useState<SelectionStep>('select')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const categories = [
    { key: 'all', label: 'All Categories' },
    { key: 'health', label: 'Health & Medical' },
    { key: 'time_off', label: 'Time Off' },
    { key: 'financial', label: 'Financial' },
    { key: 'development', label: 'Development' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'work_life', label: 'Work-Life Balance' },
    { key: 'other', label: 'Other' },
  ]

  useEffect(() => {
    if (isOpen) {
      fetchAllBenefits()
      setSelectedBenefitIds(new Set())
      setSearchTerm('')
      setSelectedCategory('all')
      setCurrentStep('select')
      setError(null)
    }
  }, [isOpen])

  const fetchAllBenefits = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/benefits')
      if (response.ok) {
        const data = await response.json()
        setAllBenefits(data)
      } else {
        setError('Failed to load benefits')
      }
    } catch (error) {
      console.error('Error fetching benefits:', error)
      setError('Failed to load benefits')
    } finally {
      setLoading(false)
    }
  }

  // Filter available benefits (not already added to company)
  const availableBenefits = useMemo(() => {
    return allBenefits.filter(benefit => 
      !existingBenefitIds.includes(benefit.id)
    )
  }, [allBenefits, existingBenefitIds])

  // Filter benefits based on search and category
  const filteredBenefits = useMemo(() => {
    return availableBenefits.filter(benefit => {
      const matchesSearch = benefit.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || benefit.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [availableBenefits, searchTerm, selectedCategory])

  // Group filtered benefits by category
  const groupedBenefits = useMemo(() => {
    const grouped = filteredBenefits.reduce((acc, benefit) => {
      const category = benefit.category || 'other'
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(benefit)
      return acc
    }, {} as Record<string, Benefit[]>)

    // Sort categories and benefits within each category
    const sortedGrouped: Record<string, Benefit[]> = {}
    categories.slice(1).forEach(cat => { // Skip 'all' category
      if (grouped[cat.key] && grouped[cat.key].length > 0) {
        sortedGrouped[cat.key] = grouped[cat.key].sort((a, b) => a.name.localeCompare(b.name))
      }
    })

    return sortedGrouped
  }, [filteredBenefits])

  const selectedBenefits = useMemo(() => {
    return allBenefits.filter(benefit => selectedBenefitIds.has(benefit.id))
  }, [allBenefits, selectedBenefitIds])

  const handleBenefitToggle = (benefitId: string) => {
    const newSelected = new Set(selectedBenefitIds)
    if (newSelected.has(benefitId)) {
      newSelected.delete(benefitId)
    } else {
      newSelected.add(benefitId)
    }
    setSelectedBenefitIds(newSelected)
  }

  const handleSelectAll = () => {
    const newSelected = new Set(selectedBenefitIds)
    filteredBenefits.forEach(benefit => {
      newSelected.add(benefit.id)
    })
    setSelectedBenefitIds(newSelected)
  }

  const handleDeselectAll = () => {
    const filteredIds = new Set(filteredBenefits.map(b => b.id))
    const newSelected = new Set(
      Array.from(selectedBenefitIds).filter(id => !filteredIds.has(id))
    )
    setSelectedBenefitIds(newSelected)
  }

  const handleProceedToReview = () => {
    if (selectedBenefitIds.size > 0) {
      setCurrentStep('review')
    }
  }

  const handleBackToSelection = () => {
    setCurrentStep('select')
  }

  const handleRemoveFromSelection = (benefitId: string) => {
    const newSelected = new Set(selectedBenefitIds)
    newSelected.delete(benefitId)
    setSelectedBenefitIds(newSelected)
  }

  const handleSubmitBatch = async () => {
    if (selectedBenefitIds.size === 0) return

    setCurrentStep('submitting')
    setError(null)

    try {
      const response = await fetch(`/api/companies/${companyId}/benefits/bulk`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          benefitIds: Array.from(selectedBenefitIds),
          action: 'add'
        })
      })

      if (response.ok) {
        const result = await response.json()
        onSuccess()
        onClose()
      } else {
        const error = await response.json()
        setError(error.error || 'Failed to add benefits')
        setCurrentStep('review')
      }
    } catch (error) {
      console.error('Error adding benefits:', error)
      setError('Failed to add benefits')
      setCurrentStep('review')
    }
  }

  const handleClose = () => {
    if (currentStep !== 'submitting') {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {currentStep === 'select' && 'Select Benefits'}
                {currentStep === 'review' && 'Review Selection'}
                {currentStep === 'submitting' && 'Adding Benefits'}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {currentStep === 'select' && `Choose multiple benefits to add to ${companyName}`}
                {currentStep === 'review' && `Review and confirm ${selectedBenefitIds.size} selected benefit${selectedBenefitIds.size !== 1 ? 's' : ''}`}
                {currentStep === 'submitting' && 'Please wait while we add the benefits...'}
              </p>
            </div>
            {currentStep !== 'submitting' && (
              <Button variant="outline" onClick={handleClose}>
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Progress indicator */}
          <div className="flex items-center mt-4 space-x-2">
            <div className={`w-3 h-3 rounded-full ${currentStep === 'select' ? 'bg-blue-600' : 'bg-green-600'}`} />
            <div className={`flex-1 h-1 ${currentStep === 'review' || currentStep === 'submitting' ? 'bg-green-600' : 'bg-gray-200'}`} />
            <div className={`w-3 h-3 rounded-full ${currentStep === 'review' || currentStep === 'submitting' ? 'bg-blue-600' : 'bg-gray-200'}`} />
            <div className={`flex-1 h-1 ${currentStep === 'submitting' ? 'bg-green-600' : 'bg-gray-200'}`} />
            <div className={`w-3 h-3 rounded-full ${currentStep === 'submitting' ? 'bg-blue-600' : 'bg-gray-200'}`} />
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {currentStep === 'select' && (
            <SelectionStep
              filteredBenefits={filteredBenefits}
              groupedBenefits={groupedBenefits}
              selectedBenefitIds={selectedBenefitIds}
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              categories={categories}
              loading={loading}
              onBenefitToggle={handleBenefitToggle}
              onSearchChange={setSearchTerm}
              onCategoryChange={setSelectedCategory}
              onSelectAll={handleSelectAll}
              onDeselectAll={handleDeselectAll}
            />
          )}

          {currentStep === 'review' && (
            <ReviewStep
              selectedBenefits={selectedBenefits}
              onRemoveFromSelection={handleRemoveFromSelection}
            />
          )}

          {currentStep === 'submitting' && (
            <SubmittingStep selectedCount={selectedBenefitIds.size} />
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {selectedBenefitIds.size} benefit{selectedBenefitIds.size !== 1 ? 's' : ''} selected
            </div>
            
            <div className="flex items-center space-x-3">
              {currentStep === 'select' && (
                <>
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleProceedToReview}
                    disabled={selectedBenefitIds.size === 0}
                    className="flex items-center gap-2"
                  >
                    Review Selection
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </>
              )}

              {currentStep === 'review' && (
                <>
                  <Button variant="outline" onClick={handleBackToSelection}>
                    Back to Selection
                  </Button>
                  <Button
                    onClick={handleSubmitBatch}
                    disabled={selectedBenefitIds.size === 0}
                    className="flex items-center gap-2"
                  >
                    Add {selectedBenefitIds.size} Benefit{selectedBenefitIds.size !== 1 ? 's' : ''}
                    <Plus className="w-4 h-4" />
                  </Button>
                </>
              )}

              {currentStep === 'submitting' && (
                <div className="flex items-center gap-2 text-blue-600">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Adding benefits...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Selection Step Component
interface SelectionStepProps {
  filteredBenefits: Benefit[]
  groupedBenefits: Record<string, Benefit[]>
  selectedBenefitIds: Set<string>
  searchTerm: string
  selectedCategory: string
  categories: Array<{ key: string; label: string }>
  loading: boolean
  onBenefitToggle: (benefitId: string) => void
  onSearchChange: (term: string) => void
  onCategoryChange: (category: string) => void
  onSelectAll: () => void
  onDeselectAll: () => void
}

function SelectionStep({
  filteredBenefits,
  groupedBenefits,
  selectedBenefitIds,
  searchTerm,
  selectedCategory,
  categories,
  loading,
  onBenefitToggle,
  onSearchChange,
  onCategoryChange,
  onSelectAll,
  onDeselectAll
}: SelectionStepProps) {
  return (
    <div className="p-6 overflow-y-auto max-h-[60vh]">
      {/* Search and Filter Controls */}
      <div className="space-y-4 mb-6">
        <div className="relative">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search benefits..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div className="flex items-center justify-between">
          <select
            value={selectedCategory}
            onChange={(e) => onCategoryChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {categories.map(category => (
              <option key={category.key} value={category.key}>
                {category.label}
              </option>
            ))}
          </select>

          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" onClick={onSelectAll}>
              Select All Visible
            </Button>
            <Button size="sm" variant="outline" onClick={onDeselectAll}>
              Deselect All Visible
            </Button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading benefits...</p>
        </div>
      ) : filteredBenefits.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600">
            {searchTerm || selectedCategory !== 'all' 
              ? 'No benefits found matching your criteria.' 
              : 'All benefits have been added to this company.'}
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedBenefits).map(([categoryKey, benefits]) => {
            const categoryLabel = categories.find(c => c.key === categoryKey)?.label || categoryKey
            
            return (
              <div key={categoryKey} className="space-y-3">
                <h4 className="font-medium text-gray-900 text-sm uppercase tracking-wide">
                  {categoryLabel}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {benefits.map((benefit) => (
                    <div
                      key={benefit.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedBenefitIds.has(benefit.id)
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => onBenefitToggle(benefit.id)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-5 h-5 border-2 rounded flex items-center justify-center ${
                          selectedBenefitIds.has(benefit.id)
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {selectedBenefitIds.has(benefit.id) && (
                            <Check className="w-3 h-3 text-white" />
                          )}
                        </div>
                        {benefit.icon && (
                          <span className="text-lg">{benefit.icon}</span>
                        )}
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900">{benefit.name}</h5>
                          <p className="text-sm text-gray-500">
                            {benefit.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Review Step Component
interface ReviewStepProps {
  selectedBenefits: Benefit[]
  onRemoveFromSelection: (benefitId: string) => void
}

function ReviewStep({ selectedBenefits, onRemoveFromSelection }: ReviewStepProps) {
  const groupedSelected = selectedBenefits.reduce((acc, benefit) => {
    const category = benefit.category || 'other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push(benefit)
    return acc
  }, {} as Record<string, Benefit[]>)

  const categories = [
    { key: 'health', label: 'Health & Medical' },
    { key: 'time_off', label: 'Time Off' },
    { key: 'financial', label: 'Financial' },
    { key: 'development', label: 'Development' },
    { key: 'wellness', label: 'Wellness' },
    { key: 'work_life', label: 'Work-Life Balance' },
    { key: 'other', label: 'Other' },
  ]

  return (
    <div className="p-6 overflow-y-auto max-h-[60vh]">
      <div className="space-y-6">
        {categories.map(category => {
          const categoryBenefits = groupedSelected[category.key]
          if (!categoryBenefits || categoryBenefits.length === 0) return null

          return (
            <div key={category.key} className="space-y-3">
              <h4 className="font-medium text-gray-900 text-sm uppercase tracking-wide">
                {category.label} ({categoryBenefits.length})
              </h4>
              <div className="space-y-2">
                {categoryBenefits.map((benefit) => (
                  <div
                    key={benefit.id}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-green-50"
                  >
                    <div className="flex items-center space-x-3">
                      {benefit.icon && (
                        <span className="text-lg">{benefit.icon}</span>
                      )}
                      <div>
                        <h5 className="font-medium text-gray-900">{benefit.name}</h5>
                        <p className="text-sm text-gray-500">
                          {benefit.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onRemoveFromSelection(benefit.id)}
                      className="text-red-600 hover:text-red-700 hover:border-red-300"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Submitting Step Component
interface SubmittingStepProps {
  selectedCount: number
}

function SubmittingStep({ selectedCount }: SubmittingStepProps) {
  return (
    <div className="p-6 flex items-center justify-center min-h-[300px]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h4 className="text-lg font-medium text-gray-900 mb-2">Adding Benefits</h4>
        <p className="text-gray-600">
          Please wait while we add {selectedCount} benefit{selectedCount !== 1 ? 's' : ''} to the company...
        </p>
      </div>
    </div>
  )
}
